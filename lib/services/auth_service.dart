import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../database/database_helper.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  static const String _userIdKey = 'user_id';
  static const String _userEmailKey = 'user_email';
  static const String _isLoggedInKey = 'is_logged_in';

  factory AuthService() => _instance;
  AuthService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper();
  User? _currentUser;

  // Get current user
  User? get currentUser => _currentUser;

  // Check if user is logged in
  bool get isLoggedIn => _currentUser != null;

  // Initialize auth service - check if user is already logged in
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
    
    if (isLoggedIn) {
      final userId = prefs.getString(_userIdKey);
      if (userId != null) {
        _currentUser = await _databaseHelper.getUserById(userId);
        if (_currentUser == null) {
          // User not found in database, clear session
          await logout();
        }
      }
    }
  }

  // Register new user
  Future<AuthResult> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  }) async {
    try {
      // Validate input
      if (!User.isValidEmail(email)) {
        return AuthResult.failure('Please enter a valid email address');
      }

      if (!User.isValidPassword(password)) {
        return AuthResult.failure(
          'Password must be at least 8 characters long and contain uppercase, lowercase, and number',
        );
      }

      if (firstName.trim().isEmpty || lastName.trim().isEmpty) {
        return AuthResult.failure('Please enter your first and last name');
      }

      // Register user in database
      final user = await _databaseHelper.registerUser(
        email: email.toLowerCase().trim(),
        password: password,
        firstName: firstName.trim(),
        lastName: lastName.trim(),
      );

      if (user == null) {
        return AuthResult.failure('Email already exists');
      }

      // Save session
      await _saveUserSession(user);
      _currentUser = user;

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('Registration failed: ${e.toString()}');
    }
  }

  // Login user
  Future<AuthResult> login(String email, String password) async {
    try {
      if (email.trim().isEmpty || password.isEmpty) {
        return AuthResult.failure('Please enter email and password');
      }

      final user = await _databaseHelper.loginUser(
        email.toLowerCase().trim(),
        password,
      );

      if (user == null) {
        return AuthResult.failure('Invalid email or password');
      }

      // Save session
      await _saveUserSession(user);
      _currentUser = user;

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('Login failed: ${e.toString()}');
    }
  }

  // Logout user
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userIdKey);
    await prefs.remove(_userEmailKey);
    await prefs.setBool(_isLoggedInKey, false);
    _currentUser = null;
  }

  // Update user profile
  Future<AuthResult> updateProfile(User updatedUser) async {
    try {
      final result = await _databaseHelper.updateUser(updatedUser);
      if (result > 0) {
        _currentUser = updatedUser;
        await _saveUserSession(updatedUser);
        return AuthResult.success(updatedUser);
      } else {
        return AuthResult.failure('Failed to update profile');
      }
    } catch (e) {
      return AuthResult.failure('Update failed: ${e.toString()}');
    }
  }

  // Change password
  Future<AuthResult> changePassword(String oldPassword, String newPassword) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('User not logged in');
      }

      if (!User.isValidPassword(newPassword)) {
        return AuthResult.failure(
          'Password must be at least 8 characters long and contain uppercase, lowercase, and number',
        );
      }

      final success = await _databaseHelper.changePassword(
        _currentUser!.email,
        oldPassword,
        newPassword,
      );

      if (success) {
        return AuthResult.success(_currentUser!);
      } else {
        return AuthResult.failure('Current password is incorrect');
      }
    } catch (e) {
      return AuthResult.failure('Password change failed: ${e.toString()}');
    }
  }

  // Delete account
  Future<AuthResult> deleteAccount(String password) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('User not logged in');
      }

      // Verify password before deletion
      final user = await _databaseHelper.loginUser(_currentUser!.email, password);
      if (user == null) {
        return AuthResult.failure('Password is incorrect');
      }

      // Delete user account
      final success = await _databaseHelper.deleteUser(_currentUser!.id);
      if (success) {
        await logout();
        return AuthResult.success(null);
      } else {
        return AuthResult.failure('Failed to delete account');
      }
    } catch (e) {
      return AuthResult.failure('Account deletion failed: ${e.toString()}');
    }
  }

  // Save user session to SharedPreferences
  Future<void> _saveUserSession(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, user.id);
    await prefs.setString(_userEmailKey, user.email);
    await prefs.setBool(_isLoggedInKey, true);
  }

  // Get user ID for database queries
  String? get currentUserId => _currentUser?.id;
}

// Authentication result class
class AuthResult {
  final bool isSuccess;
  final String? errorMessage;
  final User? user;

  AuthResult._({
    required this.isSuccess,
    this.errorMessage,
    this.user,
  });

  factory AuthResult.success(User? user) {
    return AuthResult._(isSuccess: true, user: user);
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(isSuccess: false, errorMessage: message);
  }
}
