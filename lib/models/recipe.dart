class Recipe {
  final String id;
  final String userId;
  final String title;
  final String description;
  final List<String> ingredients;
  final List<String> instructions;
  final int cookingTimeMinutes;
  final int servings;
  final String category;
  final DateTime createdAt;
  final DateTime updatedAt;

  Recipe({
    required this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.ingredients,
    required this.instructions,
    required this.cookingTimeMinutes,
    required this.servings,
    required this.category,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert Recipe to Map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'description': description,
      'ingredients': ingredients.join('|'), // Store as pipe-separated string
      'instructions': instructions.join('|'), // Store as pipe-separated string
      'cookingTimeMinutes': cookingTimeMinutes,
      'servings': servings,
      'category': category,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Create Recipe from Map (database retrieval)
  factory Recipe.fromMap(Map<String, dynamic> map) {
    return Recipe(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      ingredients:
          map['ingredients'] != null
              ? (map['ingredients'] as String)
                  .split('|')
                  .where((s) => s.isNotEmpty)
                  .toList()
              : [],
      instructions:
          map['instructions'] != null
              ? (map['instructions'] as String)
                  .split('|')
                  .where((s) => s.isNotEmpty)
                  .toList()
              : [],
      cookingTimeMinutes: map['cookingTimeMinutes'] ?? 0,
      servings: map['servings'] ?? 1,
      category: map['category'] ?? 'Other',
      createdAt: DateTime.parse(
        map['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  // Create a copy of Recipe with updated fields
  Recipe copyWith({
    String? id,
    String? userId,
    String? title,
    String? description,
    List<String>? ingredients,
    List<String>? instructions,
    int? cookingTimeMinutes,
    int? servings,
    String? category,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Recipe(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
      instructions: instructions ?? this.instructions,
      cookingTimeMinutes: cookingTimeMinutes ?? this.cookingTimeMinutes,
      servings: servings ?? this.servings,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Recipe{id: $id, title: $title, category: $category, cookingTime: ${cookingTimeMinutes}min, servings: $servings}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Recipe && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
