import 'dart:async';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import '../models/recipe.dart';
import '../models/user.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'recipes.db');
    return await openDatabase(
      path,
      version: 2,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create users table
    await db.execute('''
      CREATE TABLE users(
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        profileImagePath TEXT,
        createdAt TEXT,
        updatedAt TEXT,
        isActive INTEGER DEFAULT 1
      )
    ''');

    // Create user_credentials table
    await db.execute('''
      CREATE TABLE user_credentials(
        email TEXT PRIMARY KEY,
        passwordHash TEXT NOT NULL,
        FOREIGN KEY (email) REFERENCES users (email) ON DELETE CASCADE
      )
    ''');

    // Create recipes table with user association
    await db.execute('''
      CREATE TABLE recipes(
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        ingredients TEXT,
        instructions TEXT,
        cookingTimeMinutes INTEGER,
        servings INTEGER,
        category TEXT,
        createdAt TEXT,
        updatedAt TEXT,
        FOREIGN KEY (userId) REFERENCES users (id) ON DELETE CASCADE
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Add users table
      await db.execute('''
        CREATE TABLE users(
          id TEXT PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          firstName TEXT NOT NULL,
          lastName TEXT NOT NULL,
          profileImagePath TEXT,
          createdAt TEXT,
          updatedAt TEXT,
          isActive INTEGER DEFAULT 1
        )
      ''');

      // Add user_credentials table
      await db.execute('''
        CREATE TABLE user_credentials(
          email TEXT PRIMARY KEY,
          passwordHash TEXT NOT NULL,
          FOREIGN KEY (email) REFERENCES users (email) ON DELETE CASCADE
        )
      ''');

      // Add userId column to recipes table
      await db.execute('ALTER TABLE recipes ADD COLUMN userId TEXT');

      // For existing recipes, we'll need to handle this in the app logic
      // since we don't have a default user yet
    }
  }

  // Insert a new recipe
  Future<int> insertRecipe(Recipe recipe) async {
    final db = await database;
    return await db.insert('recipes', recipe.toMap());
  }

  // Get all recipes for a user
  Future<List<Recipe>> getAllRecipes({String? userId}) async {
    final db = await database;
    List<Map<String, dynamic>> maps;

    if (userId != null) {
      maps = await db.query(
        'recipes',
        where: 'userId = ?',
        whereArgs: [userId],
        orderBy: 'updatedAt DESC',
      );
    } else {
      // For backward compatibility, get all recipes
      maps = await db.query('recipes', orderBy: 'updatedAt DESC');
    }
    return List.generate(maps.length, (i) => Recipe.fromMap(maps[i]));
  }

  // Get recipe by ID
  Future<Recipe?> getRecipeById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'recipes',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Recipe.fromMap(maps.first);
    }
    return null;
  }

  // Update recipe
  Future<int> updateRecipe(Recipe recipe) async {
    final db = await database;
    return await db.update(
      'recipes',
      recipe.toMap(),
      where: 'id = ?',
      whereArgs: [recipe.id],
    );
  }

  // Delete recipe
  Future<int> deleteRecipe(String id) async {
    final db = await database;
    return await db.delete('recipes', where: 'id = ?', whereArgs: [id]);
  }

  // Search recipes by title or category for a user
  Future<List<Recipe>> searchRecipes(String query, {String? userId}) async {
    final db = await database;
    List<Map<String, dynamic>> maps;

    if (userId != null) {
      maps = await db.query(
        'recipes',
        where: '(title LIKE ? OR category LIKE ?) AND userId = ?',
        whereArgs: ['%$query%', '%$query%', userId],
        orderBy: 'updatedAt DESC',
      );
    } else {
      maps = await db.query(
        'recipes',
        where: 'title LIKE ? OR category LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: 'updatedAt DESC',
      );
    }
    return List.generate(maps.length, (i) => Recipe.fromMap(maps[i]));
  }

  // Get recipes by category for a user
  Future<List<Recipe>> getRecipesByCategory(
    String category, {
    String? userId,
  }) async {
    final db = await database;
    List<Map<String, dynamic>> maps;

    if (userId != null) {
      maps = await db.query(
        'recipes',
        where: 'category = ? AND userId = ?',
        whereArgs: [category, userId],
        orderBy: 'updatedAt DESC',
      );
    } else {
      maps = await db.query(
        'recipes',
        where: 'category = ?',
        whereArgs: [category],
        orderBy: 'updatedAt DESC',
      );
    }
    return List.generate(maps.length, (i) => Recipe.fromMap(maps[i]));
  }

  // Get all unique categories for a user
  Future<List<String>> getAllCategories({String? userId}) async {
    final db = await database;
    List<Map<String, dynamic>> maps;

    if (userId != null) {
      maps = await db.rawQuery(
        'SELECT DISTINCT category FROM recipes WHERE userId = ? ORDER BY category',
        [userId],
      );
    } else {
      maps = await db.rawQuery(
        'SELECT DISTINCT category FROM recipes ORDER BY category',
      );
    }
    return maps.map((map) => map['category'] as String).toList();
  }

  // USER AUTHENTICATION METHODS

  // Register a new user
  Future<User?> registerUser({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  }) async {
    final db = await database;

    try {
      // Check if user already exists
      final existingUser = await getUserByEmail(email);
      if (existingUser != null) {
        return null; // User already exists
      }

      final now = DateTime.now();
      final user = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        email: email,
        firstName: firstName,
        lastName: lastName,
        createdAt: now,
        updatedAt: now,
      );

      // Insert user
      await db.insert('users', user.toMap());

      // Insert credentials
      final credentials = UserCredentials(
        email: email,
        passwordHash: User.hashPassword(password),
      );
      await db.insert('user_credentials', credentials.toMap());

      return user;
    } catch (e) {
      return null;
    }
  }

  // Login user
  Future<User?> loginUser(String email, String password) async {
    final db = await database;

    try {
      // Get user credentials
      final credentialMaps = await db.query(
        'user_credentials',
        where: 'email = ?',
        whereArgs: [email],
      );

      if (credentialMaps.isEmpty) {
        return null; // User not found
      }

      final credentials = UserCredentials.fromMap(credentialMaps.first);
      final hashedPassword = User.hashPassword(password);

      if (credentials.passwordHash != hashedPassword) {
        return null; // Wrong password
      }

      // Get user details
      return await getUserByEmail(email);
    } catch (e) {
      return null;
    }
  }

  // Get user by email
  Future<User?> getUserByEmail(String email) async {
    final db = await database;
    final maps = await db.query(
      'users',
      where: 'email = ? AND isActive = 1',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  // Get user by ID
  Future<User?> getUserById(String id) async {
    final db = await database;
    final maps = await db.query(
      'users',
      where: 'id = ? AND isActive = 1',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  // Update user profile
  Future<int> updateUser(User user) async {
    final db = await database;
    return await db.update(
      'users',
      user.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  // Change password
  Future<bool> changePassword(
    String email,
    String oldPassword,
    String newPassword,
  ) async {
    final db = await database;

    try {
      // Verify old password
      final user = await loginUser(email, oldPassword);
      if (user == null) {
        return false; // Wrong old password
      }

      // Update password
      await db.update(
        'user_credentials',
        {'passwordHash': User.hashPassword(newPassword)},
        where: 'email = ?',
        whereArgs: [email],
      );

      return true;
    } catch (e) {
      return false;
    }
  }

  // Delete user account
  Future<bool> deleteUser(String userId) async {
    final db = await database;

    try {
      // Get user email first
      final user = await getUserById(userId);
      if (user == null) return false;

      // Delete user (cascades to credentials and recipes)
      await db.delete('users', where: 'id = ?', whereArgs: [userId]);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
