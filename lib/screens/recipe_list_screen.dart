import 'package:flutter/material.dart';
import '../models/recipe.dart';
import '../database/database_helper.dart';
import '../widgets/recipe_card.dart';
import '../services/auth_service.dart';
import 'add_edit_recipe_screen.dart';
import 'recipe_detail_screen.dart';
import 'settings/settings_screen.dart';

class RecipeListScreen extends StatefulWidget {
  const RecipeListScreen({super.key});

  @override
  State<RecipeListScreen> createState() => _RecipeListScreenState();
}

class _RecipeListScreenState extends State<RecipeListScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final AuthService _authService = AuthService();
  List<Recipe> _recipes = [];
  List<Recipe> _filteredRecipes = [];
  List<String> _categories = [];
  String _selectedCategory = 'All';
  String _searchQuery = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecipes();
    _loadCategories();
  }

  Future<void> _loadRecipes() async {
    setState(() => _isLoading = true);
    try {
      final recipes = await _databaseHelper.getAllRecipes();
      setState(() {
        _recipes = recipes;
        _filteredRecipes = recipes;
        _isLoading = false;
      });
      _applyFilters();
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading recipes: $e')));
      }
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _databaseHelper.getAllCategories();
      setState(() {
        _categories = ['All', ...categories];
      });
    } catch (e) {
      // Handle error silently for categories
    }
  }

  void _applyFilters() {
    List<Recipe> filtered = _recipes;

    // Apply category filter
    if (_selectedCategory != 'All') {
      filtered =
          filtered
              .where((recipe) => recipe.category == _selectedCategory)
              .toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered =
          filtered
              .where(
                (recipe) =>
                    recipe.title.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ||
                    recipe.description.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ||
                    recipe.category.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ),
              )
              .toList();
    }

    setState(() {
      _filteredRecipes = filtered;
    });
  }

  Future<void> _deleteRecipe(Recipe recipe) async {
    try {
      await _databaseHelper.deleteRecipe(recipe.id);
      await _loadRecipes();
      await _loadCategories();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${recipe.title} deleted successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error deleting recipe: $e')));
      }
    }
  }

  void _navigateToAddRecipe() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddEditRecipeScreen()),
    );
    if (result == true) {
      await _loadRecipes();
      await _loadCategories();
    }
  }

  void _navigateToEditRecipe(Recipe recipe) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditRecipeScreen(recipe: recipe),
      ),
    );
    if (result == true) {
      await _loadRecipes();
      await _loadCategories();
    }
  }

  void _navigateToRecipeDetail(Recipe recipe) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RecipeDetailScreen(recipe: recipe),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Recipes'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_categories.length > 1)
            PopupMenuButton<String>(
              icon: const Icon(Icons.filter_list),
              onSelected: (category) {
                setState(() {
                  _selectedCategory = category;
                });
                _applyFilters();
              },
              itemBuilder:
                  (context) =>
                      _categories
                          .map(
                            (category) => PopupMenuItem(
                              value: category,
                              child: Row(
                                children: [
                                  if (category == _selectedCategory)
                                    const Icon(Icons.check, size: 20),
                                  if (category == _selectedCategory)
                                    const SizedBox(width: 8),
                                  Text(category),
                                ],
                              ),
                            ),
                          )
                          .toList(),
            ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search recipes...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _applyFilters();
              },
            ),
          ),
          // Recipe list
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredRecipes.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                      itemCount: _filteredRecipes.length,
                      itemBuilder: (context, index) {
                        final recipe = _filteredRecipes[index];
                        return RecipeCard(
                          recipe: recipe,
                          onTap: () => _navigateToRecipeDetail(recipe),
                          onEdit: () => _navigateToEditRecipe(recipe),
                          onDelete: () => _deleteRecipe(recipe),
                        );
                      },
                    ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddRecipe,
        tooltip: 'Add Recipe',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.restaurant_menu, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != 'All'
                ? 'No recipes found'
                : 'No recipes yet',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != 'All'
                ? 'Try adjusting your search or filters'
                : 'Add your first recipe to get started',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isEmpty && _selectedCategory == 'All') ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _navigateToAddRecipe,
              icon: const Icon(Icons.add),
              label: const Text('Add Recipe'),
            ),
          ],
        ],
      ),
    );
  }
}
