import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/recipe.dart';
import '../database/database_helper.dart';

class AddEditRecipeScreen extends StatefulWidget {
  final Recipe? recipe;

  const AddEditRecipeScreen({super.key, this.recipe});

  @override
  State<AddEditRecipeScreen> createState() => _AddEditRecipeScreenState();
}

class _AddEditRecipeScreenState extends State<AddEditRecipeScreen> {
  final _formKey = GlobalKey<FormState>();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _cookingTimeController;
  late TextEditingController _servingsController;
  
  List<TextEditingController> _ingredientControllers = [];
  List<TextEditingController> _instructionControllers = [];
  
  String _selectedCategory = 'Main Course';
  final List<String> _categories = [
    'Appetizer',
    'Main Course',
    'Dessert',
    'Beverage',
    'Snack',
    'Breakfast',
    'Lunch',
    'Dinner',
    'Other',
  ];

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    final recipe = widget.recipe;
    
    _titleController = TextEditingController(text: recipe?.title ?? '');
    _descriptionController = TextEditingController(text: recipe?.description ?? '');
    _cookingTimeController = TextEditingController(
      text: recipe?.cookingTimeMinutes.toString() ?? '',
    );
    _servingsController = TextEditingController(
      text: recipe?.servings.toString() ?? '',
    );

    if (recipe != null) {
      _selectedCategory = recipe.category;
      
      // Initialize ingredient controllers
      _ingredientControllers = recipe.ingredients
          .map((ingredient) => TextEditingController(text: ingredient))
          .toList();
      
      // Initialize instruction controllers
      _instructionControllers = recipe.instructions
          .map((instruction) => TextEditingController(text: instruction))
          .toList();
    }

    // Add empty controllers if none exist
    if (_ingredientControllers.isEmpty) {
      _addIngredientField();
    }
    if (_instructionControllers.isEmpty) {
      _addInstructionField();
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _cookingTimeController.dispose();
    _servingsController.dispose();
    
    for (var controller in _ingredientControllers) {
      controller.dispose();
    }
    for (var controller in _instructionControllers) {
      controller.dispose();
    }
    
    super.dispose();
  }

  void _addIngredientField() {
    setState(() {
      _ingredientControllers.add(TextEditingController());
    });
  }

  void _removeIngredientField(int index) {
    if (_ingredientControllers.length > 1) {
      setState(() {
        _ingredientControllers[index].dispose();
        _ingredientControllers.removeAt(index);
      });
    }
  }

  void _addInstructionField() {
    setState(() {
      _instructionControllers.add(TextEditingController());
    });
  }

  void _removeInstructionField(int index) {
    if (_instructionControllers.length > 1) {
      setState(() {
        _instructionControllers[index].dispose();
        _instructionControllers.removeAt(index);
      });
    }
  }

  Future<void> _saveRecipe() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final ingredients = _ingredientControllers
          .map((controller) => controller.text.trim())
          .where((text) => text.isNotEmpty)
          .toList();

      final instructions = _instructionControllers
          .map((controller) => controller.text.trim())
          .where((text) => text.isNotEmpty)
          .toList();

      final now = DateTime.now();
      final recipe = Recipe(
        id: widget.recipe?.id ?? const Uuid().v4(),
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        ingredients: ingredients,
        instructions: instructions,
        cookingTimeMinutes: int.parse(_cookingTimeController.text),
        servings: int.parse(_servingsController.text),
        category: _selectedCategory,
        createdAt: widget.recipe?.createdAt ?? now,
        updatedAt: now,
      );

      if (widget.recipe == null) {
        await _databaseHelper.insertRecipe(recipe);
      } else {
        await _databaseHelper.updateRecipe(recipe);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.recipe == null
                  ? 'Recipe added successfully!'
                  : 'Recipe updated successfully!',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving recipe: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.recipe == null ? 'Add Recipe' : 'Edit Recipe'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveRecipe,
              child: const Text('Save'),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Title
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Recipe Title *',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a recipe title';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Description
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            // Category
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items: _categories.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
            ),
            const SizedBox(height: 16),

            // Cooking time and servings row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _cookingTimeController,
                    decoration: const InputDecoration(
                      labelText: 'Cooking Time (min) *',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Required';
                      }
                      if (int.tryParse(value) == null || int.parse(value) <= 0) {
                        return 'Invalid time';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _servingsController,
                    decoration: const InputDecoration(
                      labelText: 'Servings *',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Required';
                      }
                      if (int.tryParse(value) == null || int.parse(value) <= 0) {
                        return 'Invalid servings';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Ingredients section
            _buildSectionHeader('Ingredients', _addIngredientField),
            ..._buildIngredientFields(),
            const SizedBox(height: 24),

            // Instructions section
            _buildSectionHeader('Instructions', _addInstructionField),
            ..._buildInstructionFields(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, VoidCallback onAdd) {
    return Row(
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: onAdd,
          icon: const Icon(Icons.add_circle),
          tooltip: 'Add $title',
        ),
      ],
    );
  }

  List<Widget> _buildIngredientFields() {
    return _ingredientControllers.asMap().entries.map((entry) {
      final index = entry.key;
      final controller = entry.value;
      
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: controller,
                decoration: InputDecoration(
                  labelText: 'Ingredient ${index + 1}',
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (index == 0 && (value == null || value.trim().isEmpty)) {
                    return 'At least one ingredient is required';
                  }
                  return null;
                },
              ),
            ),
            if (_ingredientControllers.length > 1)
              IconButton(
                onPressed: () => _removeIngredientField(index),
                icon: const Icon(Icons.remove_circle, color: Colors.red),
              ),
          ],
        ),
      );
    }).toList();
  }

  List<Widget> _buildInstructionFields() {
    return _instructionControllers.asMap().entries.map((entry) {
      final index = entry.key;
      final controller = entry.value;
      
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: TextFormField(
                controller: controller,
                decoration: InputDecoration(
                  labelText: 'Step ${index + 1}',
                  border: const OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) {
                  if (index == 0 && (value == null || value.trim().isEmpty)) {
                    return 'At least one instruction is required';
                  }
                  return null;
                },
              ),
            ),
            if (_instructionControllers.length > 1)
              IconButton(
                onPressed: () => _removeInstructionField(index),
                icon: const Icon(Icons.remove_circle, color: Colors.red),
              ),
          ],
        ),
      );
    }).toList();
  }
}
