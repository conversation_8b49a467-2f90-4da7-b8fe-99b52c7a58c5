import 'package:flutter/material.dart';

class HelpSupportScreen extends StatelessWidget {
  const HelpSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.help_center,
                    size: 80,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'How can we help you?',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Find answers to common questions and learn how to use Recipe App effectively.',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),

            // Getting Started
            _buildHelpSection(
              context,
              'Getting Started',
              Icons.rocket_launch,
              [
                _buildFAQItem(
                  'How do I create my first recipe?',
                  'Tap the + button on the main screen, fill in the recipe details including title, ingredients, and instructions, then tap Save.',
                ),
                _buildFAQItem(
                  'How do I organize my recipes?',
                  'Use categories to organize your recipes. You can assign categories when creating or editing recipes, and filter by category on the main screen.',
                ),
                _buildFAQItem(
                  'Can I edit my recipes after saving?',
                  'Yes! Tap the menu button (⋮) on any recipe card and select "Edit", or tap the edit button when viewing recipe details.',
                ),
              ],
            ),

            // Account Management
            _buildHelpSection(
              context,
              'Account Management',
              Icons.account_circle,
              [
                _buildFAQItem(
                  'How do I update my profile?',
                  'Go to Settings > Edit Profile to update your name, email, and other personal information.',
                ),
                _buildFAQItem(
                  'How do I change my password?',
                  'Go to Settings > Change Password, enter your current password and choose a new secure password.',
                ),
                _buildFAQItem(
                  'Is my data safe?',
                  'Yes! All your data is stored locally on your device and is never transmitted to external servers. Your password is encrypted for security.',
                ),
              ],
            ),

            // Recipe Management
            _buildHelpSection(
              context,
              'Recipe Management',
              Icons.restaurant_menu,
              [
                _buildFAQItem(
                  'How do I search for recipes?',
                  'Use the search bar at the top of the main screen to search by recipe title, description, or category.',
                ),
                _buildFAQItem(
                  'Can I delete recipes?',
                  'Yes, tap the menu button (⋮) on any recipe card and select "Delete". You\'ll be asked to confirm the deletion.',
                ),
                _buildFAQItem(
                  'How do I add multiple ingredients?',
                  'When creating or editing a recipe, tap the + button next to "Ingredients" to add more ingredient fields.',
                ),
              ],
            ),

            // Troubleshooting
            _buildHelpSection(
              context,
              'Troubleshooting',
              Icons.build,
              [
                _buildFAQItem(
                  'The app is running slowly',
                  'Try restarting the app. If the problem persists, you may have too many recipes stored. Consider organizing or archiving older recipes.',
                ),
                _buildFAQItem(
                  'I forgot my password',
                  'Currently, password recovery is not available since all data is stored locally. You may need to create a new account.',
                ),
                _buildFAQItem(
                  'My recipes disappeared',
                  'This shouldn\'t happen as data is stored locally. Check if you\'re signed into the correct account. If the issue persists, contact support.',
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Contact Support
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Theme.of(context).primaryColor.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.support_agent,
                    size: 48,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Still need help?',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'If you couldn\'t find the answer to your question, our support team is here to help.',
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      // Navigate to contact us screen
                    },
                    icon: const Icon(Icons.contact_support),
                    label: const Text('Contact Support'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Tips and Tricks
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.amber[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb, color: Colors.amber[700]),
                      const SizedBox(width: 8),
                      Text(
                        'Pro Tips',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.amber[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text('• Use descriptive titles for easy searching'),
                  const Text('• Add cooking time and servings for meal planning'),
                  const Text('• Use categories to organize by meal type or cuisine'),
                  const Text('• Include prep time in your instructions'),
                  const Text('• Add notes about ingredient substitutions'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> items,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...items,
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildFAQItem(String question, String answer) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Text(
              answer,
              style: const TextStyle(height: 1.5),
            ),
          ),
        ],
      ),
    );
  }
}
