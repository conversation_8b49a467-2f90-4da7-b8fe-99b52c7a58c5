import 'package:flutter/material.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Privacy Policy',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Last updated: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),

            _buildSection(
              context,
              'Information We Collect',
              'Recipe App is designed to work entirely offline with local storage. We collect and store the following information locally on your device:\n\n'
              '• Account information (name, email address)\n'
              '• Recipe data (titles, ingredients, instructions, categories)\n'
              '• App usage preferences and settings\n\n'
              'All data is stored locally on your device using SQLite database and is not transmitted to external servers.',
            ),

            _buildSection(
              context,
              'How We Use Your Information',
              'Your information is used solely for:\n\n'
              '• Providing app functionality and features\n'
              '• Personalizing your recipe collection\n'
              '• Maintaining your account and preferences\n'
              '• Improving app performance and user experience\n\n'
              'We do not share, sell, or transmit your personal information to third parties.',
            ),

            _buildSection(
              context,
              'Data Storage and Security',
              'Your data is stored locally on your device using:\n\n'
              '• SQLite database for recipe and user data\n'
              '• SharedPreferences for app settings\n'
              '• Password encryption using SHA-256 hashing\n\n'
              'We implement security measures to protect your data, but please note that no method of electronic storage is 100% secure.',
            ),

            _buildSection(
              context,
              'Data Retention',
              'Your data remains on your device until:\n\n'
              '• You delete the app\n'
              '• You delete your account\n'
              '• You manually delete specific recipes or data\n\n'
              'When you delete your account, all associated data is permanently removed from your device.',
            ),

            _buildSection(
              context,
              'Your Rights',
              'You have the right to:\n\n'
              '• Access your personal data\n'
              '• Update or correct your information\n'
              '• Delete your account and all associated data\n'
              '• Export your recipe data\n\n'
              'You can exercise these rights through the app settings.',
            ),

            _buildSection(
              context,
              'Children\'s Privacy',
              'Our app is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13.',
            ),

            _buildSection(
              context,
              'Changes to This Policy',
              'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy in the app. Changes are effective immediately upon posting.',
            ),

            _buildSection(
              context,
              'Contact Us',
              'If you have any questions about this Privacy Policy, please contact us through the Contact Us section in the app settings.',
            ),

            const SizedBox(height: 32),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue[600]),
                      const SizedBox(width: 8),
                      Text(
                        'Offline First',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[600],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Recipe App is designed with privacy in mind. All your data stays on your device and is never transmitted to external servers or third parties.',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          content,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
