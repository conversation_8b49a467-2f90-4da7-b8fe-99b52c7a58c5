// This is a basic Flutter widget test for the Recipe App.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:recipe_app/main.dart';

void main() {
  testWidgets('Recipe app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const RecipeApp());

    // Wait for the database to load
    await tester.pumpAndSettle();

    // Verify that the app loads with the correct title
    expect(find.text('My Recipes'), findsOneWidget);

    // Verify that the floating action button is present
    expect(find.byType(FloatingActionButton), findsOneWidget);

    // Verify that the empty state is shown initially (after loading)
    expect(find.text('No recipes yet'), findsOneWidget);
    expect(find.text('Add your first recipe to get started'), findsOneWidget);
  });

  testWidgets('Add recipe button works', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const RecipeApp());

    // Tap the floating action button
    await tester.tap(find.byType(FloatingActionButton));
    await tester.pumpAndSettle();

    // Verify that the add recipe screen opens
    expect(find.text('Add Recipe'), findsOneWidget);
    expect(find.text('Recipe Title *'), findsOneWidget);
  });
}
